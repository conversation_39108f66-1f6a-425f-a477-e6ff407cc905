import 'package:flutter/material.dart';
import 'pages/splash_screen.dart';
import 'pages/login_page.dart';
import 'pages/main_screen.dart';
import 'pages/user_agreement_page.dart';
import 'config/app_config.dart';
import 'config/pocketbase_config.dart';
import 'services/service_locator.dart';
import 'utils/database_migration_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化 PocketBase 客户端（允许失败）
  try {
    await PocketBaseConfig.instance.initialize();
  } catch (e) {
    // PocketBase 初始化失败时，记录错误但不阻止应用启动
    debugPrint('PocketBase 初始化失败: $e');
    debugPrint('应用将在离线模式下启动');
  }

  // 初始化服务定位器
  try {
    debugPrint('开始初始化服务架构...');
    await serviceLocator.registerServices();
    await serviceLocator.initializeServices();
    serviceLocator.printServiceStatus();
    debugPrint('服务架构初始化完成');
  } catch (e) {
    debugPrint('服务架构初始化失败: $e');
    debugPrint('应用将使用降级模式启动');
  }

  // 执行数据库健康检查
  try {
    debugPrint('开始数据库健康检查...');
    final healthCheck = await DatabaseMigrationHelper.performHealthCheck();

    if (!healthCheck['structure']) {
      debugPrint('⚠️ 数据库结构不完整，付费查看功能可能无法正常工作');
      DatabaseMigrationHelper.showDatabaseFixGuide();
    }
  } catch (e) {
    debugPrint('数据库健康检查失败: $e');
  }

  // 打印配置信息（仅在开发模式下）
  AppConfig.instance.printConfigInfo();

  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 延迟检测剪贴板，确保应用完全启动
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 2), () {
        _checkClipboardForShareLink();
      });
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 应用从后台恢复时检测剪贴板
    if (state == AppLifecycleState.resumed) {
      _checkClipboardForShareLink();
    }
  }

  /// 检测剪贴板中的分享链接
  Future<void> _checkClipboardForShareLink() async {
    try {
      if (!mounted) return;

      final spotId = await Services.share.detectShareLink();
      if (spotId != null && mounted) {
        // 使用调度器确保在下一帧执行，避免BuildContext问题
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          if (!mounted) return;

          final context = _navigatorKey.currentContext;
          if (context != null) {
            try {
              // 显示确认对话框
              final shouldOpen = await Services.share.showShareLinkDialog(
                context,
                spotId,
              );
              if (shouldOpen && mounted && context.mounted) {
                // 处理分享链接跳转
                await Services.share.handleShareLink(context, spotId);
              }
            } catch (e) {
              debugPrint('❌ [分享链接处理] 处理失败: $e');
            }
          }
        });
      }
    } catch (e) {
      debugPrint('❌ [应用启动] 检测剪贴板失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: _navigatorKey,
      title: '钓鱼了么',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color.fromARGB(255, 0, 136, 255),
        ),
        useMaterial3: true,
      ),
      initialRoute: '/splash',
      routes: {
        '/splash': (context) => const SplashScreen(),
        '/login': (context) => const LoginPage(),
        '/main': (context) => const MainScreen(),
        '/user-agreement': (context) => const UserAgreementPage(),
      },
    );
  }
}
