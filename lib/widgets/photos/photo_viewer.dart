import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../../services/unified_image_service.dart';
import 'photo_models.dart';

/// 全屏照片查看器
///
/// 功能：
/// - 全屏显示照片
/// - 支持缩放和滑动
/// - 支持轮播切换
/// - 支持关闭按钮
class PhotoViewer extends StatefulWidget {
  /// 照片列表
  final List<PhotoItem> photos;

  /// 初始显示的照片索引
  final int initialIndex;

  /// 是否显示关闭按钮
  final bool showCloseButton;

  /// 是否显示页面指示器
  final bool showPageIndicator;

  /// 背景颜色
  final Color backgroundColor;

  /// 关闭回调
  final VoidCallback? onClose;

  /// 页面变化回调
  final Function(int index)? onPageChanged;

  const PhotoViewer({
    super.key,
    required this.photos,
    this.initialIndex = 0,
    this.showCloseButton = true,
    this.showPageIndicator = true,
    this.backgroundColor = Colors.black,
    this.onClose,
    this.onPageChanged,
  });

  @override
  State<PhotoViewer> createState() => _PhotoViewerState();
}

class _PhotoViewerState extends State<PhotoViewer> {
  final UnifiedImageService _imageService = UnifiedImageService();
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex.clamp(0, widget.photos.length - 1);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.backgroundColor,
      body: Stack(
        children: [
          // 照片轮播
          Center(
            child: CarouselSlider.builder(
              itemCount: widget.photos.length,
              itemBuilder: (context, index, realIndex) {
                final photo = widget.photos[index];
                return InteractiveViewer(child: _buildPhotoForViewer(photo));
              },
              options: CarouselOptions(
                height: double.infinity,
                viewportFraction: 1.0,
                enableInfiniteScroll: widget.photos.length > 1,
                initialPage: _currentIndex,
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentIndex = index;
                  });
                  widget.onPageChanged?.call(index);
                },
              ),
            ),
          ),

          // 关闭按钮
          if (widget.showCloseButton) _buildCloseButton(),

          // 页面指示器
          if (widget.showPageIndicator && widget.photos.length > 1)
            _buildPageIndicator(),
        ],
      ),
    );
  }

  /// 构建查看器专用的照片
  Widget _buildPhotoForViewer(PhotoItem photo) {
    return _imageService.buildCachedSignedImage(
      originalUrl: photo.originalUrl, // 使用原图
      fit: BoxFit.contain,
      placeholder: const Center(
        child: CircularProgressIndicator(color: Colors.white),
      ),
      errorWidget: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.broken_image, size: 64, color: Colors.white54),
            SizedBox(height: 16),
            Text(
              '图片加载失败',
              style: TextStyle(color: Colors.white54, fontSize: 16),
            ),
          ],
        ),
      ),
      isAvatar: photo.isAvatar,
    );
  }

  /// 构建关闭按钮
  Widget _buildCloseButton() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      right: 20,
      child: GestureDetector(
        onTap: () {
          widget.onClose?.call();
          Navigator.of(context).pop();
        },
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(Icons.close, color: Colors.white, size: 24),
        ),
      ),
    );
  }

  /// 构建页面指示器
  Widget _buildPageIndicator() {
    return Positioned(
      bottom: MediaQuery.of(context).padding.bottom + 20,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            '${_currentIndex + 1} / ${widget.photos.length}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// 显示照片查看器（静态方法）
  static Future<void> show(
    BuildContext context, {
    required List<PhotoItem> photos,
    int initialIndex = 0,
    bool showCloseButton = true,
    bool showPageIndicator = true,
    Color backgroundColor = Colors.black,
    Function(int index)? onPageChanged,
  }) {
    if (photos.isEmpty) return Future.value();

    return showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder:
          (context) => PhotoViewer(
            photos: photos,
            initialIndex: initialIndex,
            showCloseButton: showCloseButton,
            showPageIndicator: showPageIndicator,
            backgroundColor: backgroundColor,
            onPageChanged: onPageChanged,
          ),
    );
  }

  /// 显示全屏照片查看器（静态方法）
  static Future<void> showFullscreen(
    BuildContext context, {
    required List<PhotoItem> photos,
    int initialIndex = 0,
    Function(int index)? onPageChanged,
  }) {
    if (photos.isEmpty) return Future.value();

    return Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) => PhotoViewer(
              photos: photos,
              initialIndex: initialIndex,
              onPageChanged: onPageChanged,
            ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 300),
        barrierColor: Colors.black,
        opaque: false,
      ),
    );
  }
}
