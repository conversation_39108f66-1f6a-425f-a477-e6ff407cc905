import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import '../../lib/services/unified_image_service.dart';

void main() {
  group('UnifiedImageService', () {
    late UnifiedImageService service;

    setUp(() {
      service = UnifiedImageService();
    });

    test('should be singleton', () {
      final service1 = UnifiedImageService();
      final service2 = UnifiedImageService();
      expect(service1, same(service2));
    });

    test('should extract object key from R2 URL correctly', () async {
      const testUrl = 'https://pub-123456789.r2.dev/fishing-app/avatars/user123/avatar.jpg';
      
      // 这个测试需要模拟网络请求，暂时跳过
      // 实际测试需要在集成测试中进行
    });

    testWidgets('buildSignedImage should create widget', (WidgetTester tester) async {
      const testUrl = 'https://pub-123456789.r2.dev/fishing-app/avatars/user123/avatar.jpg';
      
      final widget = service.buildSignedImage(
        originalUrl: testUrl,
        width: 100,
        height: 100,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: widget,
          ),
        ),
      );

      // 应该显示加载指示器
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('buildSignedAvatar should create avatar widget', (WidgetTester tester) async {
      const testUrl = 'https://pub-123456789.r2.dev/fishing-app/avatars/user123/avatar.jpg';
      
      final widget = service.buildSignedAvatar(
        originalUrl: testUrl,
        radius: 50,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: widget,
          ),
        ),
      );

      // 应该显示CircleAvatar
      expect(find.byType(CircleAvatar), findsOneWidget);
    });

    test('clearCache should clear all cached URLs', () {
      // 这个测试需要访问私有成员，暂时跳过
      // 可以通过集成测试验证缓存功能
      service.clearCache();
      // 验证缓存已清空
    });
  });
}
