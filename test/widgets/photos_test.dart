import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/widgets/photos/photos.dart';

void main() {
  group('照片组件测试', () {
    final testPhotos = [
      PhotoItem.fromUrl(
        id: '1',
        url: 'https://example.com/photo1.jpg',
        description: '测试照片1',
      ),
      PhotoItem.fromUrl(
        id: '2',
        url: 'https://example.com/photo2.jpg',
        description: '测试照片2',
      ),
    ];

    testWidgets('PhotoGallery 轮播模式应该正常渲染', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PhotoGallery(
              photos: testPhotos,
              config: PhotoGalleryConfig.carousel(),
            ),
          ),
        ),
      );

      // 验证轮播组件存在
      expect(find.byType(PhotoCarousel), findsOneWidget);
    });

    testWidgets('PhotoGallery 网格模式应该正常渲染', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PhotoGallery(
              photos: testPhotos,
              config: PhotoGalleryConfig.grid(),
            ),
          ),
        ),
      );

      // 验证网格组件存在
      expect(find.byType(PhotoGrid), findsOneWidget);
    });

    testWidgets('PhotoGallery 紧凑模式应该正常渲染', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PhotoGallery(
              photos: testPhotos,
              config: PhotoGalleryConfig.compact(),
            ),
          ),
        ),
      );

      // 验证轮播组件存在（紧凑模式使用PhotoCarousel）
      expect(find.byType(PhotoCarousel), findsOneWidget);
    });

    testWidgets('PhotoItem 应该正确创建', (WidgetTester tester) async {
      final photoItem = PhotoItem.fromUrl(
        id: 'test-id',
        url: 'https://example.com/test.jpg',
        description: '测试照片',
        type: PhotoType.normal,
      );

      expect(photoItem.id, equals('test-id'));
      expect(photoItem.url, equals('https://example.com/test.jpg'));
      expect(photoItem.description, equals('测试照片'));
      expect(photoItem.type, equals(PhotoType.normal));
      expect(photoItem.displayUrl, equals('https://example.com/test.jpg'));
      expect(photoItem.originalUrl, equals('https://example.com/test.jpg'));
      expect(photoItem.isPanorama, isFalse);
      expect(photoItem.isAvatar, isFalse);
    });

    test('PhotoGalleryConfig 工厂方法应该正确创建配置', () {
      // 测试轮播配置
      final carouselConfig = PhotoGalleryConfig.carousel(height: 300);
      expect(carouselConfig.mode, equals(PhotoDisplayMode.carousel));
      expect(carouselConfig.carouselHeight, equals(300));

      // 测试网格配置
      final gridConfig = PhotoGalleryConfig.grid(crossAxisCount: 4);
      expect(gridConfig.mode, equals(PhotoDisplayMode.grid));
      expect(gridConfig.gridCrossAxisCount, equals(4));

      // 测试紧凑配置
      final compactConfig = PhotoGalleryConfig.compact(height: 150);
      expect(compactConfig.mode, equals(PhotoDisplayMode.compact));
      expect(compactConfig.compactHeight, equals(150));
    });
  });
}
