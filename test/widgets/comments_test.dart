import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/widgets/comments/comments.dart';

void main() {
  group('评论组件测试', () {
    testWidgets('CommentInputBar 应该正常渲染', (WidgetTester tester) async {
      bool submitted = false;
      String submittedContent = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CommentInputBar(
              onSubmit: (content) async {
                submitted = true;
                submittedContent = content;
                return true;
              },
            ),
          ),
        ),
      );

      // 验证输入框存在
      expect(find.byType(TextField), findsOneWidget);
      
      // 验证发送按钮存在
      expect(find.byIcon(Icons.send), findsOneWidget);
      
      // 输入文本
      await tester.enterText(find.byType(TextField), '测试评论');
      await tester.pump();
      
      // 点击发送按钮
      await tester.tap(find.byIcon(Icons.send));
      await tester.pump();
      
      // 验证提交回调被调用
      expect(submitted, isTrue);
      expect(submittedContent, equals('测试评论'));
    });

    testWidgets('CommentSystem 应该正常渲染', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CommentSystem(
              targetId: 'test-spot-id',
              type: CommentType.spot,
              title: '测试评论',
            ),
          ),
        ),
      );

      // 验证标题存在
      expect(find.text('测试评论'), findsOneWidget);
      
      // 验证评论输入栏存在
      expect(find.byType(CommentInputBar), findsOneWidget);
      
      // 验证评论列表存在
      expect(find.byType(CommentListView), findsOneWidget);
    });
  });
}
